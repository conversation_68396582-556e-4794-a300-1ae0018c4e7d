#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def parse_utc_time(utc_str):
    """解析设备发送的UTC时间并转换为北京时间"""
    try:
        if len(utc_str) == 12:  # 格式: HHmmss.DDMMYY
            # 提取时间部分
            hour = int(utc_str[0:2])
            minute = utc_str[2:4]
            second = utc_str[4:6]

            # 提取日期部分（在点号后面）
            date_part = utc_str.split('.')[1]
            day = date_part[0:2]
            month = date_part[2:4]
            year = date_part[4:6]

            # 转换为北京时间（UTC+8）
            beijing_hour = (hour + 8) % 24

            # 如果跨天了，需要调整日期
            day_adjust = 1 if (hour + 8) >= 24 else 0
            if day_adjust:
                # 创建datetime对象进行日期计算
                from datetime import datetime, timedelta
                base_date = datetime(2000 + int(year), int(month), int(day))
                adjusted_date = base_date + timedelta(days=day_adjust)
                year = str(adjusted_date.year - 2000).zfill(2)
                month = str(adjusted_date.month).zfill(2)
                day = str(adjusted_date.day).zfill(2)

            return f"20{year}-{month}-{day} {str(beijing_hour).zfill(2)}:{minute}:{second}"
    except Exception as e:
        logging.error(f"解析时间失败: {str(e)}")
    return utc_str

def get_gps_status(status):
    """获取GPS状态描述"""
    status_dict = {
        '0': '未定位',
        '2': '2D定位',
        '3': '3D定位'
    }
    return status_dict.get(status, '未知状态')

def parse_hy_data(data):
    """解析HY开头的数据"""
    try:
        # 检查数据格式: HY[长度][S/B]+数据+E
        if len(data) < 6:
            logging.error(f"HY数据长度不足: {data}")
            return

        # 找到第一个+号的位置来确定长度和类型字段
        first_plus = data.find('+')
        if first_plus == -1:
            logging.error(f"HY数据格式错误，未找到+号: {data}")
            return

        # 提取长度和类型部分 (例如: HY110S)
        header = data[:first_plus]  # HY110S
        if len(header) < 4:
            logging.error(f"HY数据头部格式错误: {header}")
            return

        # 提取数据类型 (S或B)
        data_type = header[-1]  # S或B
        length_str = header[2:-1]  # 110

        if not length_str.isdigit():
            logging.error(f"HY数据长度格式错误: {length_str}")
            return

        # 验证数据类型
        if data_type not in ['S', 'B']:
            logging.error(f"HY数据类型错误: {data_type}")
            return

        # 检查数据是否以E结尾
        if not data.endswith('E'):
            logging.error(f"HY数据格式错误，未以E结尾: {data}")
            return

        # 提取数据部分（去掉开头的HY110S+和结尾的+E）
        data_content = data[first_plus+1:-2]  # 去掉开头的HY110S+和结尾的+E
        parts = data_content.split('+')

        print(f"HY数据解析: 头部={header}, 类型={data_type}, 字段数={len(parts)}")
        print(f"数据字段: {parts}")

        if len(parts) >= 17:  # HY格式包含17个字段
            data_type_desc = "实时数据" if data_type == 'S' else "历史数据"

            # 解析UTC时间
            utc_time = parts[2]  # UTC时间
            time_str = parse_utc_time(utc_time)

            # 格式化数据
            parsed_data = f"""
[{data_type_desc}] HY格式数据:
位置: 经度={parts[0]}, 纬度={parts[1]}
时间: {time_str}
海拔: {parts[3]}m
GPS状态: {parts[4]} ({get_gps_status(parts[4])})
卫星数量: {parts[5]}
速度: {parts[6]}节
电池电压: {parts[7]}V
温度: {parts[8]}°C
HDOP: {parts[9]}
PDOP: {parts[10]}
陀螺仪: X={parts[11]}, Y={parts[12]}, Z={parts[13]}
方位角: {parts[14]}
GSM信号: {parts[15]}
ICCID: {parts[16] if len(parts) > 16 else 'N/A'}
"""
            print(parsed_data)
        else:
            logging.error(f"HY数据字段不足: {len(parts)} < 17")

    except Exception as e:
        logging.error(f"解析HY数据失败: {str(e)}")

def test_hy_data_parsing():
    """测试HY数据解析"""
    # 测试数据
    test_data = "HY110S+119.96198+30.27607+094436.020725+33.1+1+10+0.1+3.61+26.5+2.2+5.0+6.0+-4.7+0.0+0.00+26+89852312278524093476+E"

    print("测试数据:")
    print(test_data)
    print("\n" + "="*50)

    # 解析数据
    parse_hy_data(test_data)

if __name__ == "__main__":
    test_hy_data_parsing()
