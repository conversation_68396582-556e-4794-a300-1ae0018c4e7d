from flask import Flask, render_template, jsonify, request
import threading
import logging
import queue
import datetime

app = Flask(__name__)

# 禁用Flask默认的访问日志
app.logger.disabled = True
logging.getLogger('werkzeug').disabled = True

# 用于存储最新的设备数据
device_data = []
# 用于存储原始数据
raw_data = []
# 最大保存记录数
MAX_RECORDS = 100
# 用于存储实时日志
log_queue = queue.Queue(maxsize=1000)
# 用于存储当前连接的设备
active_clients = {}
# TCP服务器引用
tcp_server = None

def add_log_handler():
    class QueueHandler(logging.Handler):
        def emit(self, record):
            # 记录原始数据相关的日志和HB数据记录数量
            keywords = ['收到数据', '新的连接', '发送配置', 'HB数据包含记录数']
            if any(keyword in record.getMessage() for keyword in keywords):
                # 如果是解析数据的日志则跳过
                if '解析数据' in record.getMessage():
                    return
                log_queue.put({
                    'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'level': record.levelname,
                    'message': record.getMessage()
                })
                # 保持队列大小
                if log_queue.qsize() > 1000:
                    try:
                        log_queue.get_nowait()
                    except:
                        pass

    # 添加队列处理器到根日志记录器
    logging.getLogger().addHandler(QueueHandler())

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/data')
def get_data():
    return jsonify(device_data)

@app.route('/api/logs')
def get_logs():
    logs = []
    try:
        while not log_queue.empty():
            logs.append(log_queue.get_nowait())
    except:
        pass
    return jsonify(logs)

@app.route('/api/raw_data')
def get_raw_data():
    return jsonify(raw_data)

@app.route('/api/send_command', methods=['POST'])
def send_command():
    cmd = request.json.get('command')
    if not cmd:
        return jsonify({'status': 'error', 'message': '命令不能为空'})

    try:
        logging.info(f"收到配置请求: {cmd}")

        if tcp_server is None:
            logging.error("TCP服务器未初始化")
            return jsonify({'status': 'error', 'message': 'TCP服务器未初始化，请检查服务器状态'})

        # 更新TCP服务器的配置指令
        result = tcp_server.set_config_command(cmd)
        if result:
            logging.info(f"配置已保存: {cmd}，将在设备下次连接时发送")
            return jsonify({'status': 'success', 'message': '配置已保存，将在设备下次连接时发送'})
        else:
            logging.error(f"配置指令格式错误: {cmd}")
            return jsonify({'status': 'error', 'message': '配置格式不正确'})
    except Exception as e:
        logging.error(f"处理配置请求异常: {str(e)}")
        return jsonify({'status': 'error', 'message': f'配置更新失败: {str(e)}'})

def add_device_data(data):
    device_data.insert(0, {
        'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'data': data
    })
    # 保持最大记录数
    if len(device_data) > MAX_RECORDS:
        device_data.pop()

def add_raw_data(data):
    raw_data.insert(0, {
        'time': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'data': data
    })
    # 保持最大记录数
    if len(raw_data) > MAX_RECORDS:
        raw_data.pop()

def init_tcp_server(server):
    global tcp_server
    tcp_server = server
    logging.info("TCP服务器引用初始化成功")

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
