echo "" > /root/device_tcp_server.py
nano device_tcp_server.py


激活虚拟环境：
source venv/bin/activate

启动服务器：
python3 device_tcp_server.py

设备-服务器通信协议说明
1. 连接信息
服务器地址: http://47.117.43.95:5000 
端口: 48085
协议: TCP

http://47.117.43.95:5000


公司后台网址：https://search.blwkkj.com/index.php?method=index/admin/mobile
myadmin
blwk24682021!


2. 数据格式
设备发送的数据格式为字符串，格式如下：
HX+[经度]+[纬度]+[UTC时间]+[海拔]+[GPS定位状态]+[速度]+[电池电压]+[温度]+[HDOP]+[PDOP]+[陀螺仪X]+[陀螺仪Y]+[陀螺仪Z]+[方位角]+[GSM信号]+E+[ICCID]

字段说明:
HX: 固定头部标识
经度: 格式如"11957.64581"
纬度: 格式如"3016.51036"
UTC时间: 格式如"065026.220524" (时分秒.日月年)
海拔: 单位米，如"39.7"
GPS定位状态: 单字符，'2'=2D定位，'3'=3D定位
速度: 单位节，如"1.55"
电池电压: 实际电压+100mV，如"4059"表示3.959V
温度: 整数值
HDOP: 水平精度因子，如"2.0"
PDOP: 位置精度因子，如"3.0"
陀螺仪XYZ: 三轴角度值，整数
方位角: 如"74.85"
GSM信号: 整数值(0-31)
E: 固定分隔符
ICCID: SIM卡号

示例:HX+11957.64581+3016.51036+065026.220524+39.7+3*****+4059+32********+-6+-9+-79+74.85+15+E+89852312278524043828

3. 服务器响应
设备发送数据后，等待服务器返回"ACCEPT"作为确认。如果收到确认：
更新数据读取指针
减少待发送数据计数
继续发送下一包数据
如果未收到确认或收到"FAIL"：
保存当前数据到本地存储
等待下次唤醒重试
4. 自定义指令
服务器可以下发以下格式的指令来配置设备：
ZL[参数类型][值]

参数类型:
D: 设置天数
H: 设置小时
M: 设置分钟
S: 设置秒数
N: 设置存储次数
G: 设置发送间隔时间
A: 设置速度参数
B: 设置速度参数时的发送间隔
F: 设置工作时间段
5. 调试建议
搭建TCP服务器，监听指定端口
实现简单的数据解析和"ACCEPT"响应
记录接收到的数据进行分析
可以通过下发指令测试设备配置功能




