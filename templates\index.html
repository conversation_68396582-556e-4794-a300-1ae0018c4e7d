<!DOCTYPE html>
<html>
<head>
    <title>设备监控系统</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        :root {
            --primary-color: #3498db;
            --secondary-color: #2ecc71;
            --dark-color: #34495e;
            --light-color: #f5f5f5;
            --border-color: #ddd;
            --shadow: 0 2px 10px rgba(0,0,0,0.1);
            --accent-color: #e74c3c;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.4;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 15px;
        }

        .header {
            background-color: var(--dark-color);
            color: white;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 5px;
            box-shadow: var(--shadow);
        }

        .header h1 {
            margin: 0;
            font-size: 22px;
        }

        .panel {
            background: white;
            padding: 12px;
            margin-bottom: 15px;
            border-radius: 5px;
            box-shadow: var(--shadow);
        }

        .panel-title {
            margin-top: 0;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid var(--border-color);
            font-size: 16px;
            color: var(--dark-color);
        }

        .control-panel {
            margin-bottom: 15px;
        }

        .config-options {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .input-row {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .input-group {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-right: 15px;
        }

        input[type="text"],
        input[type="number"] {
            padding: 6px 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
        }

        input[type="text"] {
            width: 300px;
        }

        input[type="number"] {
            width: 70px;
        }

        select {
            padding: 6px 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
            background-color: white;
        }

        button {
            padding: 6px 12px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            white-space: nowrap;
            transition: background-color 0.2s;
        }

        button:hover {
            background-color: #2980b9;
        }

        button.secondary {
            background-color: var(--secondary-color);
        }

        button.secondary:hover {
            background-color: #27ae60;
        }

        .data-container {
            display: flex;
            gap: 15px;
        }

        .data-panel {
            flex: 1;
            height: 550px;
            overflow-y: auto;
            position: relative;
        }

        .data-content {
            font-family: "Consolas", monospace;
            white-space: pre-wrap;
            font-size: 13px;
            line-height: 1.3;
            height: 100%;
            overflow-y: auto;
        }

        #deviceData {
            padding: 0;
        }

        .device-info {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            background-color: rgba(52, 152, 219, 0.05);
            border-left: 3px solid var(--primary-color);
        }

        .device-info-header {
            color: var(--dark-color);
            font-weight: bold;
            margin-bottom: 4px;
            font-size: 13px;
        }

        .device-info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-gap: 2px 10px;
        }

        .device-info-item {
            display: flex;
            margin-bottom: 1px;
        }

        .device-info-label {
            width: 75px;
            font-weight: bold;
            color: var(--dark-color);
        }

        .device-info-value {
            flex: 1;
        }

        #rawData {
            font-size: 12px;
            color: #555;
        }

        .raw-data-item {
            padding: 5px;
            margin-bottom: 3px;
            border-bottom: 1px solid #eee;
        }

        .raw-data-time {
            color: var(--dark-color);
            font-weight: bold;
            font-size: 12px;
        }

        .raw-data-content {
            margin-top: 2px;
            word-break: break-all;
        }

        #logData {
            font-size: 12px;
            line-height: 1.3;
        }

        .log-item {
            margin-bottom: 2px;
        }

        .spacer {
            flex: 1;
        }

        @media (max-width: 768px) {
            .data-container {
                flex-direction: column;
            }

            .data-panel {
                height: 400px;
            }

            .input-row {
                flex-direction: column;
                align-items: stretch;
            }

            .input-group {
                margin-right: 0;
            }
        }

        .system-time {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            padding: 8px 12px;
            background-color: rgba(52, 152, 219, 0.1);
            border-radius: 4px;
        }

        .current-time {
            font-weight: bold;
            font-size: 16px;
            color: var(--dark-color);
        }

        .time-btn {
            background-color: var(--accent-color);
        }

        .time-btn:hover {
            background-color: #c0392b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>设备监控系统</h1>
        </div>

        <div class="panel control-panel">
            <h2 class="panel-title">配置控制</h2>

            <div class="system-time">
                <span>系统时间:</span>
                <span class="current-time" id="currentTime">加载中...</span>
                <button class="time-btn" onclick="sendTimeCommand()">设置设备时间</button>
            </div>

            <div class="config-options">
                <div class="input-row">
                    <input type="text" id="commandInput" placeholder="输入配置指令，如: ZL+M5, ZL+S30">
                    <button onclick="sendCommand()">发送配置</button>
                </div>

                <div class="input-row">
                    <div class="input-group">
                        <label>发送间隔:</label>
                        <input type="number" id="intervalValue" min="1" value="30">
                        <select id="intervalType">
                            <option value="S">秒</option>
                            <option value="M">分钟</option>
                        </select>
                        <button class="secondary" onclick="sendIntervalConfig()">设置间隔</button>
                    </div>

                    <div class="input-group">
                        <label>工作时间段:</label>
                        <select id="startHour">
                            <option value="">开始</option>
                        </select>
                        <span>至</span>
                        <select id="endHour">
                            <option value="">结束</option>
                        </select>
                        <button class="secondary" onclick="sendTimeConfig()">设置时间段</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="data-container">
            <div class="panel data-panel">
                <h2 class="panel-title">设备数据</h2>
                <div class="data-content" id="deviceData">
                    <!-- 设备数据会在这里格式化显示 -->
                </div>
            </div>

            <div class="panel data-panel">
                <h2 class="panel-title">原始数据</h2>
                <div class="data-content" id="rawData">
                    <!-- 原始数据会在这里显示 -->
                </div>
            </div>
        </div>

        <div class="panel">
            <h2 class="panel-title">系统日志</h2>
            <div class="data-content" id="logData">
                <!-- 日志数据会在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        function updateSystemTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
            document.getElementById('currentTime').textContent = timeString;
        }

        function sendTimeCommand() {
            const now = new Date();

            // 格式化为YYYYMMDDhhmmss
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');

            const timeString = `${year}${month}${day}${hours}${minutes}${seconds}`;
            const timeCommand = `ZL+TIME=${timeString}`;

            console.log(`准备发送时间设置指令: ${timeCommand}`);

            // 发送配置
            fetch('/api/send_command', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ command: timeCommand })
            })
            .then(response => response.json())
            .then(result => {
                if (result.status === 'success') {
                    console.log(`时间设置指令已发送: ${timeCommand}`);
                    // 不立即清除提示，等待设备响应
                    setTimeout(() => {
                        alert(`时间设置指令已发送，请查看设备响应`);
                    }, 1000);
                } else {
                    alert(`时间设置失败: ${result.message}`);
                }
            })
            .catch(error => {
                alert('设置时间失败: ' + error);
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            const startSelect = document.getElementById('startHour');
            const endSelect = document.getElementById('endHour');

            for (let i = 0; i <= 23; i++) {
                const val = i.toString().padStart(2, '0');
                const option = new Option(i + '点', val);
                startSelect.appendChild(option);
            }

            for (let i = 1; i <= 24; i++) {
                const val = i.toString().padStart(2, '0');
                const option = new Option(i + '点', val);
                endSelect.appendChild(option);
            }

            updateSystemTime();
            setInterval(updateSystemTime, 1000);
        });

        function formatDeviceData(data) {
            const timeMatch = data.match(/\[(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})\]/);
            const timestamp = timeMatch ? timeMatch[1] : '';

            const formattedData = data.replace(/\[.*?\]\n([\s\S]*)/m, (match, content) => {
                const lines = content.trim().split('\n');
                const infoItems = [];

                lines.forEach(line => {
                    const parts = line.split(':').map(p => p.trim());
                    if (parts.length >= 2) {
                        const label = parts[0];
                        const value = parts.slice(1).join(':');
                        infoItems.push(`<div class="device-info-item">
                            <span class="device-info-label">${label}</span>
                            <span class="device-info-value">${value}</span>
                        </div>`);
                    } else {
                        infoItems.push(`<div class="device-info-item">${line}</div>`);
                    }
                });

                return `<div class="device-info">
                    <div class="device-info-header">[${timestamp}]</div>
                    <div class="device-info-grid">
                        ${infoItems.join('')}
                    </div>
                </div>`;
            });

            return formattedData;
        }

        function formatRawData(data) {
            return data.map(item =>
                `<div class="raw-data-item">
                    <div class="raw-data-time">[${item.time}]</div>
                    <div class="raw-data-content">${item.data}</div>
                </div>`
            ).join('');
        }

        function updateData() {
            Promise.all([
                fetch('/api/data').then(response => response.json()),
                fetch('/api/raw_data').then(response => response.json()),
                fetch('/api/logs').then(response => response.json())
            ])
            .then(([deviceData, rawData, logs]) => {
                // 更新设备数据
                const deviceDataDiv = document.getElementById('deviceData');
                const formattedData = deviceData.map(item => formatDeviceData(
                    `[${item.time}]\n${item.data}`
                )).join('');
                deviceDataDiv.innerHTML = formattedData;

                // 更新原始数据
                const rawDataDiv = document.getElementById('rawData');
                rawDataDiv.innerHTML = formatRawData(rawData);

                // 更新日志数据
                const logDataDiv = document.getElementById('logData');
                logDataDiv.innerHTML = logs.map(log =>
                    `<div class="log-item">
                        <span class="log-time">[${log.time}]</span>
                        <span class="log-level">${log.level}:</span>
                        <span class="log-message">${log.message}</span>
                    </div>`
                ).join('');
            })
            .catch(error => {
                console.error('更新数据失败:', error);
                // 可以在页面上显示错误提示
            });
        }

        function sendCommand() {
            const command = document.getElementById('commandInput').value;
            if (!command) {
                alert('请输入配置指令');
                return;
            }

            fetch('/api/send_command', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ command: command })
            })
            .then(response => response.json())
            .then(result => {
                alert(result.message);
                if (result.status === 'success') {
                    document.getElementById('commandInput').value = '';
                }
            })
            .catch(error => {
                alert('发送指令失败: ' + error);
            });
        }

        function sendIntervalConfig() {
            const intervalValue = document.getElementById('intervalValue').value;
            const intervalType = document.getElementById('intervalType').value;

            if (!intervalValue || parseInt(intervalValue) <= 0) {
                alert('请输入有效的间隔值（大于0）');
                return;
            }

            const intervalCommand = `ZL+${intervalType}${intervalValue}`;
            console.log(`准备发送间隔配置: ${intervalCommand}`);  // 添加日志

            fetch('/api/send_command', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ command: intervalCommand })
            })
            .then(response => response.json())
            .then(result => {
                if (result.status === 'success') {
                    console.log(`间隔配置已保存: ${intervalCommand}`);
                    alert(`配置已保存，将在设备下次连接时发送`);
                } else {
                    alert(`设置失败: ${result.message}`);
                }
            })
            .catch(error => {
                console.error('设置间隔失败:', error);
                alert('设置间隔失败，请检查网络连接');
            });
        }

        function sendTimeConfig() {
            const startHour = document.getElementById('startHour').value;
            const endHour = document.getElementById('endHour').value;

            if (!startHour || !endHour) {
                alert('请选择开始和结束时间');
                return;
            }

            const timeCommand = `ZL+F${startHour}E${endHour}`;

            fetch('/api/send_command', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ command: timeCommand })
            })
            .then(response => response.json())
            .then(result => {
                alert(result.message);
            })
            .catch(error => {
                alert('设置工作时间段失败: ' + error);
            });
        }

        setInterval(updateData, 5000);
        updateData();
    </script>
</body>
</html>
