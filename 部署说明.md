# TCP设备监控服务器部署说明

## 1. 环境准备
- Linux服务器（已开通SSH访问）
- Python 3.x
- 确保服务器已开放48085和5000端口

## 2. 部署步骤

### 2.1 创建项目目录
```bash
mkdir /root/device_monitor
cd /root/device_monitor
```

### 2.2 创建并激活Python虚拟环境
```bash
python3 -m venv venv
source venv/bin/activate
```

### 2.3 安装依赖包
```bash
pip install flask
```

### 2.4 创建项目文件结构
```bash
# 创建主要的Python文件
touch device_tcp_server.py web_monitor.py

# 创建启动脚本并添加执行权限
touch start_server.sh
chmod +x start_server.sh

# 创建templates目录和index.html
mkdir templates
touch templates/index.html
```

### 2.5 配置文件内容
将以下内容复制到相应文件中：

#### start_server.sh
```bash
#!/bin/bash
cd /root/device_monitor
source venv/bin/activate
python3 device_tcp_server.py
```

### 2.6 启动服务器
有两种启动方式：

#### 方式1：直接运行
```bash
python3 device_tcp_server.py
```

#### 方式2：使用启动脚本
```bash
./start_server.sh
```

#### 方式3：后台运行（推荐）
使用nohup：
```bash
nohup ./start_server.sh > server.log 2>&1 &
```

或使用screen（需要先安装screen）：
```bash
screen -S device_monitor
./start_server.sh
# 按Ctrl+A+D离开screen会话
```

## 3. 服务检查

### 3.1 检查服务状态
```bash
# 检查端口是否在监听
netstat -tunlp | grep -E "48085|5000"

# 查看日志
tail -f tcp_server.log
```

### 3.2 访问Web监控界面
在浏览器中访问：
```
http://47.117.44.33:5000
```

## 4. 服务说明

### 4.1 端口使用
- 48085：TCP设备连接端口
- 5000：Web监控界面端口

### 4.2 主要功能
- 接收设备TCP连接和数据
- 解析设备上报的位置、状态等信息
- 提供Web界面实时监控设备数据
- 支持向设备发送配置指令

### 4.3 文件说明
- device_tcp_server.py：TCP服务器主程序
- web_monitor.py：Web监控界面程序
- start_server.sh：启动脚本
- tcp_server.log：运行日志

## 5. 常见问题处理

### 5.1 服务无法启动
- 检查端口是否被占用
- 检查Python虚拟环境是否正确激活
- 检查依赖包是否安装完整

### 5.2 设备无法连接
- 检查服务器防火墙设置
- 确认48085端口是否开放
- 检查服务器是否正常运行

### 5.3 Web界面无法访问
- 检查5000端口是否开放
- 确认服务器防火墙设置
- 检查Flask服务是否正常运行

## 6. 维护建议
- 定期检查日志文件大小，必要时进行清理
- 监控服务器资源使用情况
- 定期备份重要数据和配置文件
